<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\BuberDinner.Application\BuberDinner.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="6.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.21.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.1" />
  </ItemGroup>

</Project>
